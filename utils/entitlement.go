package utils

import (
	"strings"
	"time"

	entitlement "github.com/foxcorp-product/commerce-entitlement/client"
	entitlementService "github.com/foxcorp-product/commerce-entitlement/entitlement"

	"github.com/foxcorp-product/commerce-purchase/models"
)

const commV1SubscriptionActiveStatus = "Active"
const commV1SubscriptionFinalBillStatus = "Final Bill"

func EntitlementsToSubscriptionsCommV1(entitlements []entitlement.V1EntitlementObject) ([]models.ServiceDetails, bool) {
	if len(entitlements) == 0 {
		return []models.ServiceDetails{}, false
	}

	pkgDetails := make([]models.PackageDetails, 0, len(entitlements))
	isSubscribed := false
	for _, ent := range entitlements {
		if ent.Status != string(entitlementService.EntitlementStatusChurnedInvoluntarily) && ent.Status != string(entitlementService.EntitlementStatusChurnedVoluntary) {
			isSubscribed = true
		}
		pkgDetails = append(pkgDetails, models.PackageDetails{
			OrderID:       ent.PurchaseId,
			ServiceID:     ent.AppServiceId,
			ServiceName:   ent.Description,
			PaymentMethod: ent.PaymentMethod,
			StartDate:     ent.StartDate.UnixMilli(),
			ValidityTill:  ent.ValidityTill.UnixMilli(),
			Tier:          int64(ent.Tier),
			Status:        ent.Status,
			ProductId: []models.ProductId{
				{
					ProductId: "foxnation",
				},
			},
		})
	}
	svcDetails := []models.ServiceDetails{
		{
			AppId:          "foxnation",
			PackageDetails: pkgDetails,
		},
	}
	return svcDetails, isSubscribed
}

func SubscriptionsCommV1ToCommV2(sd []models.ServiceDetails) []models.V1SubscriptionObject {
	subsCommV2 := make([]models.V1SubscriptionObject, 0, len(sd))
	for _, v := range sd {
		for _, pkgDetails := range v.PackageDetails {
			startDate := time.UnixMilli(pkgDetails.StartDate)
			validityTill := time.UnixMilli(pkgDetails.ValidityTill)
			serviceId := pkgDetails.ServiceID
			serviceName := pkgDetails.ServiceName
			status := strings.TrimSpace(pkgDetails.Status)
			active := status == commV1SubscriptionActiveStatus ||
				(status == commV1SubscriptionFinalBillStatus && pkgDetails.ValidityTill > time.Now().UnixMilli()) // or it's in pending cancel

			subscription := models.V1SubscriptionObject{
				AppServiceId: pkgDetails.ServiceID,
				Partner:      pkgDetails.PaymentMethod,
				StartDate:    &startDate,
				Status:       pkgDetails.Status,
				Active:       active,
				ValidityTill: &validityTill,
				PurchaseId:   pkgDetails.OrderID,
				AppId:        v.AppId,
				// ProductFamily: "", // TODO: Add ProductFamily after it is better defined
				Services: []models.V1SubscriptionServiceObject{
					{
						ServiceId:   &serviceId,
						ServiceName: &serviceName,
					},
				},
			}
			subsCommV2 = append(subsCommV2, subscription)
		}
	}
	return subsCommV2
}

func EntitlementsToSubscriptions(entitlements []entitlement.V1EntitlementObject) []models.V1SubscriptionObject {
	subs := make([]models.V1SubscriptionObject, 0, len(entitlements))
	for _, v := range entitlements {
		subscription := models.V1SubscriptionObject{
			AppServiceId: v.AppServiceId,
			Partner:      v.PaymentMethod,
			StartDate:    &v.StartDate,
			Status:       v.Status,
			Active:       v.Active,
			ValidityTill: v.ValidityTill,
			PurchaseId:   v.PurchaseId,
			AppId:        v.AppId,
			// ProductFamily: v.ProductFamily, // TODO: Add ProductFamily after it is better defined
		}

		if v.PendingServiceChange != nil {
			subscription.UpcomingAppServiceId = v.PendingServiceChange.TargetAppServiceId
		}

		if len(v.Services) > 0 {
			subscription.Services = make([]models.V1SubscriptionServiceObject, 0, len(v.Services))
			for _, service := range v.Services {
				subscription.Services = append(subscription.Services, models.V1SubscriptionServiceObject{
					ServiceId:   service.ServiceId,
					ServiceName: service.ServiceName,
				})
			}
		}

		if v.Revocation != nil && v.Revocation.ResumesAt != nil {
			subscription.ResumeAt = v.Revocation.ResumesAt
		}

		if v.Revocation != nil {
			subscription.ValidityTill = &v.Revocation.Date
		}

		subs = append(subs, subscription)
	}
	return subs
}

func FilterEntitlementsByAppId(entitlements []entitlement.V1EntitlementObject, appId string) []entitlement.V1EntitlementObject {
	if len(entitlements) == 0 {
		return entitlements
	}

	return Filter(entitlements, func(entitlement entitlement.V1EntitlementObject) bool {
		return entitlement.AppId == appId
	})
}
